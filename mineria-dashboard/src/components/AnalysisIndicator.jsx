import React, { useState, useEffect } from 'react';

const AnalysisIndicator = ({ 
  isAnalyzing = false, 
  label = "Estado del Sistema", 
  size = "medium",
  showLabel = true,
  onAnalysisToggle = null 
}) => {
  const [analyzing, setAnalyzing] = useState(isAnalyzing);

  useEffect(() => {
    setAnalyzing(isAnalyzing);
  }, [isAnalyzing]);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-lg';
      case 'large':
        return 'text-3xl';
      default:
        return 'text-2xl';
    }
  };

  const handleClick = () => {
    if (onAnalysisToggle) {
      const newState = !analyzing;
      setAnalyzing(newState);
      onAnalysisToggle(newState);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div 
        className={`
          ${getSizeClasses()} 
          transition-all duration-300 
          ${onAnalysisToggle ? 'cursor-pointer hover:scale-110' : ''}
          ${analyzing ? 'animate-pulse' : ''}
        `}
        onClick={handleClick}
        title={analyzing ? "Analizando..." : "Sistema en espera"}
      >
        {analyzing ? (
          <span className="text-black">⬥</span>
        ) : (
          <span className="text-blue-500">🔷</span>
        )}
      </div>
      
      {showLabel && (
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <span className={`text-xs ${analyzing ? 'text-orange-600' : 'text-green-600'}`}>
            {analyzing ? 'Analizando...' : 'En espera'}
          </span>
        </div>
      )}
    </div>
  );
};

export default AnalysisIndicator;
