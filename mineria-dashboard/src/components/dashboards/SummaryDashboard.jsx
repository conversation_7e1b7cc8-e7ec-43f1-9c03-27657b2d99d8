import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

const SummaryDashboard = () => {
  // Sample data for production summary
  const productionData = [
    { name: 'Actual', value: 150 },
    { name: 'Objetivo', value: 500 },
    { name: 'Ideal', value: 800 },
  ];

  // Sample data for financial summary
  const financialData = [
    { name: 'Gastos Actuales', value: 120000 },
    { name: 'Gastos Optimizados', value: 120000 },
  ];

  // Sample data for personnel summary
  const personnelData = [
    { name: 'Actual', value: 17 },
    { name: 'Optimizado', value: 8 },
  ];

  // Sample data for resource status
  const resourceStatusData = [
    { name: 'Crítico', value: 3 },
    { name: 'Bajo', value: 2 },
    { name: 'Normal', value: 1 },
  ];

  const COLORS = ['#3B82F6', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6'];

  // Key metrics
  const keyMetrics = [
    { title: 'Producción Actual', value: '150 TN', change: '-70%', target: '500 TN', status: 'negative' },
    { title: 'Ahorro Mensual', value: 'S/ 37,300', change: '+68%', target: 'S/ 40,000', status: 'positive' },
    { title: 'Eficiencia Mineral', value: '25%', change: '-5%', target: '60%', status: 'negative' },
    { title: 'Personal', value: '17', change: '+3', target: '8', status: 'negative' },
  ];

  // Critical issues
  const criticalIssues = [
    { issue: 'Perforadora inoperativa', impact: 'Alto', status: 'Pendiente' },
    { issue: 'Falta de fulminantes', impact: 'Alto', status: 'En proceso' },
    { issue: 'Campamento mal ubicado', impact: 'Medio', status: 'Pendiente' },
    { issue: 'Baja productividad de volquetes', impact: 'Medio', status: 'En proceso' },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Dashboard General - Mina A</h2>
        <div className="text-sm text-gray-500">Actualizado: Mayo 2025</div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {keyMetrics.map((metric, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500">{metric.title}</h3>
            <div className="flex items-end mt-1">
              <p className="text-2xl font-bold text-gray-800">{metric.value}</p>
              <span className={`ml-2 text-sm ${metric.status === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                {metric.change}
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Meta: {metric.target}</p>
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-gray-700">Resumen de Producción</h3>
            <Link to="/production" className="text-blue-600 hover:text-blue-800 text-sm">Ver detalles →</Link>
          </div>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={productionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" fill="#3B82F6" name="Toneladas" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-gray-700">Resumen Financiero</h3>
            <Link to="/financial" className="text-blue-600 hover:text-blue-800 text-sm">Ver detalles →</Link>
          </div>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={financialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => `S/ ${value.toLocaleString()}`} />
              <Legend />
              <Bar dataKey="value" fill="#10B981" name="Soles (S/)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-gray-700">Problemas Críticos</h3>
            <Link to="/resources" className="text-blue-600 hover:text-blue-800 text-sm">Ver recursos →</Link>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-4 border-b text-left">Problema</th>
                  <th className="py-2 px-4 border-b text-left">Impacto</th>
                  <th className="py-2 px-4 border-b text-left">Estado</th>
                </tr>
              </thead>
              <tbody>
                {criticalIssues.map((issue, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b">{issue.issue}</td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${
                        issue.impact === 'Alto' ? 'bg-red-100 text-red-800' : 
                        issue.impact === 'Medio' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {issue.impact}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">
                      <span className={`px-2 py-1 rounded text-xs ${
                        issue.status === 'Pendiente' ? 'bg-red-100 text-red-800' : 
                        issue.status === 'En proceso' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {issue.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-gray-700">Estado de Recursos</h3>
            <Link to="/resources" className="text-blue-600 hover:text-blue-800 text-sm">Ver detalles →</Link>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={resourceStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {resourceStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            
            <div className="flex flex-col justify-center">
              <div className="mb-2">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                  <span className="text-sm">Crítico: Fulminantes, Nitrato, Perforadora</span>
                </div>
              </div>
              <div className="mb-2">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                  <span className="text-sm">Bajo: Emulnor, Gasolina</span>
                </div>
              </div>
              <div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                  <span className="text-sm">Normal: Víveres</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Plan de Acción Inmediato</h3>
        <ul className="list-disc pl-5 text-blue-700">
          <li>Implementar dos turnos 24/7 para maximizar producción</li>
          <li>Reubicar campamento operativo para mejorar condiciones</li>
          <li>Adquirir perforadora de reemplazo con ahorro de personal</li>
          <li>Reorganizar flujo de materiales y víveres</li>
          <li>Implementar pago a perforista por tonelada producida</li>
        </ul>
      </div>
    </div>
  );
};

export default SummaryDashboard;
