import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const FinancialDashboard = () => {
  // Sample data for current expenses
  const currentExpensesData = [
    { category: 'Personal', value: 55000 },
    { category: 'Insumos', value: 25000 },
    { category: 'Combustible', value: 15000 },
    { category: 'Mantenimiento', value: 10000 },
    { category: 'Logística', value: 8000 },
    { category: 'Otros', value: 7000 },
  ];

  // Sample data for optimized expenses
  const optimizedExpensesData = [
    { category: 'Personal', value: 17700 },
    { category: 'Insumos', value: 40000 },
    { category: 'Combustible', value: 20000 },
    { category: 'Mantenimiento', value: 25000 },
    { category: 'Logística', value: 10000 },
    { category: 'Otros', value: 7300 },
  ];

  // Sample data for reinvestment allocation
  const reinvestmentData = [
    { name: 'Explosivos', value: 15000 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 10000 },
    { name: '<PERSON>uest<PERSON>', value: 8000 },
    { name: 'Combustible', value: 5000 },
    { name: 'Energía Solar', value: 2000 },
  ];

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  // Calculate totals
  const currentTotal = currentExpensesData.reduce((sum, item) => sum + item.value, 0);
  const optimizedTotal = optimizedExpensesData.reduce((sum, item) => sum + item.value, 0);
  const reinvestmentTotal = reinvestmentData.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Dashboard Financiero - Mina A</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-red-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-red-800">Gastos Actuales</h3>
          <p className="text-3xl font-bold text-red-600">S/ {currentTotal.toLocaleString()}</p>
          <p className="text-sm text-red-500">Mensual</p>
        </div>
        
        <div className="bg-green-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800">Gastos Optimizados</h3>
          <p className="text-3xl font-bold text-green-600">S/ {optimizedTotal.toLocaleString()}</p>
          <p className="text-sm text-green-500">Mensual</p>
        </div>
        
        <div className="bg-blue-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800">Reinversión</h3>
          <p className="text-3xl font-bold text-blue-600">S/ {reinvestmentTotal.toLocaleString()}</p>
          <p className="text-sm text-blue-500">En materiales clave</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Distribución Actual de Gastos</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={currentExpensesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip formatter={(value) => `S/ ${value.toLocaleString()}`} />
              <Legend />
              <Bar dataKey="value" fill="#EF4444" name="Monto (S/)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Distribución Optimizada de Gastos</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={optimizedExpensesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip formatter={(value) => `S/ ${value.toLocaleString()}`} />
              <Legend />
              <Bar dataKey="value" fill="#10B981" name="Monto (S/)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-4 text-gray-700">Distribución de Reinversión</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={reinvestmentData}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {reinvestmentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `S/ ${value.toLocaleString()}`} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
          
          <div>
            <h4 className="text-lg font-semibold mb-3 text-gray-700">Detalles de Reinversión</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="py-2 px-4 border-b text-left">Categoría</th>
                    <th className="py-2 px-4 border-b text-left">Monto (S/)</th>
                    <th className="py-2 px-4 border-b text-left">Porcentaje</th>
                  </tr>
                </thead>
                <tbody>
                  {reinvestmentData.map((item, index) => (
                    <tr key={index}>
                      <td className="py-2 px-4 border-b">{item.name}</td>
                      <td className="py-2 px-4 border-b">{item.value.toLocaleString()}</td>
                      <td className="py-2 px-4 border-b">
                        {((item.value / reinvestmentTotal) * 100).toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-gray-50 font-semibold">
                    <td className="py-2 px-4 border-b">Total</td>
                    <td className="py-2 px-4 border-b">{reinvestmentTotal.toLocaleString()}</td>
                    <td className="py-2 px-4 border-b">100%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialDashboard;
