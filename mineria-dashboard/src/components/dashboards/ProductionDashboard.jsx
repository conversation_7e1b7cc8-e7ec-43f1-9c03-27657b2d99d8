import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import AnalysisIndicator from '../AnalysisIndicator';

const ProductionDashboard = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleAnalysisToggle = (analyzing) => {
    setIsAnalyzing(analyzing);
    if (analyzing) {
      setTimeout(() => {
        setIsAnalyzing(false);
      }, 3000);
    }
  };

  // Sample data for monthly production
  const monthlyData = [
    { name: 'Abril', actual: 150, objetivo: 500, ideal: 800 },
    { name: 'Mayo', actual: 250, objetivo: 500, ideal: 800 },
    { name: '<PERSON><PERSON>', actual: 400, objetivo: 500, ideal: 800 },
    { name: '<PERSON>', actual: 550, objetivo: 500, ideal: 800 },
    { name: '<PERSON><PERSON><PERSON>', actual: 650, objetivo: 500, ideal: 800 },
    { name: 'Septiembre', actual: 750, objetivo: 500, ideal: 800 },
  ];

  // Sample data for daily production
  const dailyData = [
    { day: '1', toneladas: 5 },
    { day: '2', toneladas: 7 },
    { day: '3', toneladas: 6 },
    { day: '4', toneladas: 8 },
    { day: '5', toneladas: 4 },
    { day: '6', toneladas: 9 },
    { day: '7', toneladas: 10 },
    { day: '8', toneladas: 8 },
    { day: '9', toneladas: 7 },
    { day: '10', toneladas: 6 },
    { day: '11', toneladas: 8 },
    { day: '12', toneladas: 9 },
    { day: '13', toneladas: 10 },
    { day: '14', toneladas: 11 },
    { day: '15', toneladas: 9 },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Dashboard de Producción - Mina A</h2>
        <AnalysisIndicator
          isAnalyzing={isAnalyzing}
          onAnalysisToggle={handleAnalysisToggle}
          label="Análisis de Producción"
          size="small"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800">Producción Actual</h3>
          <p className="text-3xl font-bold text-blue-600">150 TN</p>
          <p className="text-sm text-blue-500">Abril (6 TN/día)</p>
        </div>

        <div className="bg-yellow-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-800">Objetivo Mínimo</h3>
          <p className="text-3xl font-bold text-yellow-600">500 TN</p>
          <p className="text-sm text-yellow-500">20 TN/día</p>
        </div>

        <div className="bg-green-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800">Objetivo Ideal</h3>
          <p className="text-3xl font-bold text-green-600">800 TN</p>
          <p className="text-sm text-green-500">32 TN/día</p>
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4 text-gray-700">Producción Mensual (Toneladas)</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={monthlyData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="actual" fill="#3B82F6" name="Producción Actual" />
            <Bar dataKey="objetivo" fill="#F59E0B" name="Objetivo Mínimo" />
            <Bar dataKey="ideal" fill="#10B981" name="Objetivo Ideal" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-4 text-gray-700">Producción Diaria (Toneladas)</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={dailyData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="day" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="toneladas" stroke="#3B82F6" name="Toneladas" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ProductionDashboard;
