import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

const PersonnelDashboard = () => {
  // Sample data for current personnel costs
  const currentPersonnelData = [
    { role: 'Encargado General', cantidad: 1, costo: 3500 },
    { role: 'Perforistas', cantidad: 2, costo: 6000 },
    { role: 'Ayudantes de Perforista', cantidad: 2, costo: 4800 },
    { role: 'Ayudantes de Mina', cantidad: 3, costo: 6300 },
    { role: 'Cocinera', cantidad: 1, costo: 2500 },
    { role: 'Chófer', cantidad: 2, costo: 4000 },
    { role: 'Capataz', cantidad: 1, costo: 4500 },
    { role: 'Supervisor', cantidad: 1, costo: 5000 },
    { role: 'Administrativos', cantidad: 4, costo: 18000 },
  ];

  // Sample data for optimized personnel costs
  const optimizedPersonnelData = [
    { role: 'Encargado General', cantidad: 1, costo: 1800 },
    { role: 'Perforistas', cantidad: 2, costo: 5400 },
    { role: 'Ayudantes de Perforista', cantidad: 2, costo: 4800 },
    { role: 'Ayudante de Mina', cantidad: 1, costo: 2100 },
    { role: 'Cocinera', cantidad: 1, costo: 1800 },
    { role: 'Chófer', cantidad: 1, costo: 1800 },
  ];

  // Sample data for cost comparison
  const costComparisonData = [
    { name: 'Actual', value: 55000 },
    { name: 'Optimizado', value: 17700 },
  ];

  const COLORS = ['#EF4444', '#10B981'];

  // Calculate total costs
  const currentTotal = currentPersonnelData.reduce((sum, item) => sum + item.costo, 0);
  const optimizedTotal = optimizedPersonnelData.reduce((sum, item) => sum + item.costo, 0);
  const savings = currentTotal - optimizedTotal;

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Dashboard de Personal - Mina A</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-red-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-red-800">Costo Actual</h3>
          <p className="text-3xl font-bold text-red-600">S/ {currentTotal.toLocaleString()}</p>
          <p className="text-sm text-red-500">Planilla Mensual</p>
        </div>
        
        <div className="bg-green-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800">Costo Optimizado</h3>
          <p className="text-3xl font-bold text-green-600">S/ {optimizedTotal.toLocaleString()}</p>
          <p className="text-sm text-green-500">Planilla Mensual</p>
        </div>
        
        <div className="bg-blue-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800">Ahorro Mensual</h3>
          <p className="text-3xl font-bold text-blue-600">S/ {savings.toLocaleString()}</p>
          <p className="text-sm text-blue-500">Para reinversión</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Estructura Actual</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-4 border-b text-left">Rol</th>
                  <th className="py-2 px-4 border-b text-left">Cantidad</th>
                  <th className="py-2 px-4 border-b text-left">Costo (S/)</th>
                </tr>
              </thead>
              <tbody>
                {currentPersonnelData.map((item, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b">{item.role}</td>
                    <td className="py-2 px-4 border-b">{item.cantidad}</td>
                    <td className="py-2 px-4 border-b">{item.costo.toLocaleString()}</td>
                  </tr>
                ))}
                <tr className="bg-gray-50 font-semibold">
                  <td className="py-2 px-4 border-b">Total</td>
                  <td className="py-2 px-4 border-b">{currentPersonnelData.reduce((sum, item) => sum + item.cantidad, 0)}</td>
                  <td className="py-2 px-4 border-b">{currentTotal.toLocaleString()}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Estructura Optimizada</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-4 border-b text-left">Rol</th>
                  <th className="py-2 px-4 border-b text-left">Cantidad</th>
                  <th className="py-2 px-4 border-b text-left">Costo (S/)</th>
                </tr>
              </thead>
              <tbody>
                {optimizedPersonnelData.map((item, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b">{item.role}</td>
                    <td className="py-2 px-4 border-b">{item.cantidad}</td>
                    <td className="py-2 px-4 border-b">{item.costo.toLocaleString()}</td>
                  </tr>
                ))}
                <tr className="bg-gray-50 font-semibold">
                  <td className="py-2 px-4 border-b">Total</td>
                  <td className="py-2 px-4 border-b">{optimizedPersonnelData.reduce((sum, item) => sum + item.cantidad, 0)}</td>
                  <td className="py-2 px-4 border-b">{optimizedTotal.toLocaleString()}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-4 text-gray-700">Comparación de Costos</h3>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={costComparisonData}
              cx="50%"
              cy="50%"
              labelLine={true}
              label={({ name, value }) => `${name}: S/ ${value.toLocaleString()}`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {costComparisonData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => `S/ ${value.toLocaleString()}`} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PersonnelDashboard;
