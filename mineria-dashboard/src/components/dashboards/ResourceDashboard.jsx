import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const ResourceDashboard = () => {
  // Sample data for inventory levels
  const inventoryData = [
    { name: 'Fulminantes', actual: 30, requerido: 100 },
    { name: '<PERSON><PERSON><PERSON>', actual: 45, requerido: 100 },
    { name: '<PERSON><PERSON><PERSON>', actual: 20, requerido: 100 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', actual: 60, requerido: 100 },
    { name: '<PERSON><PERSON><PERSON>', actual: 40, requerido: 100 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', actual: 70, requerido: 100 },
  ];

  // Sample data for mineral vs desmonte
  const mineralData = [
    { name: 'Mineral', value: 25 },
    { name: 'Desmonte', value: 75 },
  ];

  const COLORS = ['#10B981', '#EF4444'];

  // Sample data for equipment status
  const equipmentData = [
    { name: 'Perfo<PERSON><PERSON>', status: 'Inoperativa', urgencia: '<PERSON><PERSON>' },
    { name: 'Volquete 1', status: 'Operativo', urgencia: 'Baja' },
    { name: 'Volquete 2', status: 'Mantenimiento', urgencia: 'Media' },
    { name: 'Compresora', status: 'Operativo', urgencia: 'Baja' },
    { name: 'Generador', status: 'Operativo', urgencia: 'Baja' },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Dashboard de Recursos - Mina A</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Niveles de Inventario</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={inventoryData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" />
              <Tooltip />
              <Legend />
              <Bar dataKey="actual" fill="#3B82F6" name="Nivel Actual (%)" />
              <Bar dataKey="requerido" fill="#D1D5DB" name="Nivel Requerido (%)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div>
          <h3 className="text-xl font-semibold mb-4 text-gray-700">Proporción Mineral vs Desmonte</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={mineralData}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {mineralData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-4 text-gray-700">Estado de Equipos</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-2 px-4 border-b text-left">Equipo</th>
                <th className="py-2 px-4 border-b text-left">Estado</th>
                <th className="py-2 px-4 border-b text-left">Urgencia</th>
                <th className="py-2 px-4 border-b text-left">Acción</th>
              </tr>
            </thead>
            <tbody>
              {equipmentData.map((item, index) => (
                <tr key={index}>
                  <td className="py-2 px-4 border-b">{item.name}</td>
                  <td className="py-2 px-4 border-b">
                    <span className={`px-2 py-1 rounded text-white ${
                      item.status === 'Operativo' ? 'bg-green-500' : 
                      item.status === 'Mantenimiento' ? 'bg-yellow-500' : 'bg-red-500'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b">
                    <span className={`px-2 py-1 rounded ${
                      item.urgencia === 'Baja' ? 'bg-green-100 text-green-800' : 
                      item.urgencia === 'Media' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.urgencia}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b">
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                      Detalles
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ResourceDashboard;
