<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Mina A - <PERSON><PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: white;
        }
        .diamond-indicator {
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: black;
        }
        .diamond-indicator:hover {
            transform: scale(1.1);
        }
        .analyzing {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-white min-h-screen">
    <!-- Navegación -->
    <nav class="bg-gradient-to-r from-blue-900 to-blue-800 text-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                    </svg>
                    <h1 class="text-2xl font-bold text-white">Mina A <span class="text-yellow-400">Dashboard</span></h1>
                </div>
                <div class="hidden md:flex space-x-4">
                    <a href="#resumen" class="px-4 py-2 rounded-md bg-blue-700 text-white">Resumen</a>
                    <a href="#produccion" class="px-4 py-2 rounded-md hover:bg-blue-700 text-white">Producción</a>
                    <a href="#recursos" class="px-4 py-2 rounded-md hover:bg-blue-700 text-white">Recursos</a>
                    <a href="#personal" class="px-4 py-2 rounded-md hover:bg-blue-700 text-white">Personal</a>
                    <a href="#finanzas" class="px-4 py-2 rounded-md hover:bg-blue-700 text-white">Finanzas</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Contenido Principal -->
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Dashboard General -->
        <div id="resumen" class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">Dashboard General - Mina A</h2>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span id="diamondIndicator" class="diamond-indicator" onclick="toggleAnalysis()" title="Click para analizar">⬥</span>
                        <div class="flex flex-col">
                            <span class="text-sm font-medium text-gray-700">Análisis de Datos</span>
                            <span id="statusText" class="text-xs text-green-600">En espera</span>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500">Actualizado: Mayo 2025</div>
                </div>
            </div>

            <!-- Métricas Clave -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500">Producción Actual</h3>
                    <div class="flex items-end mt-1">
                        <p class="text-2xl font-bold text-gray-800">150 TN</p>
                        <span class="ml-2 text-sm text-red-600">-70%</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Meta: 500 TN</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500">Ahorro Mensual</h3>
                    <div class="flex items-end mt-1">
                        <p class="text-2xl font-bold text-gray-800">S/ 37,300</p>
                        <span class="ml-2 text-sm text-green-600">+68%</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Meta: S/ 40,000</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500">Eficiencia Mineral</h3>
                    <div class="flex items-end mt-1">
                        <p class="text-2xl font-bold text-gray-800">25%</p>
                        <span class="ml-2 text-sm text-red-600">-5%</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Meta: 60%</p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500">Personal</h3>
                    <div class="flex items-end mt-1">
                        <p class="text-2xl font-bold text-gray-800">17</p>
                        <span class="ml-2 text-sm text-red-600">+3</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Meta: 8</p>
                </div>
            </div>

            <!-- Gráficos de Resumen -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-700">Resumen de Producción</h3>
                    <div class="bg-white p-4 rounded-lg border border-gray-200" style="height: 300px;">
                        <canvas id="productionChart"></canvas>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-700">Problemas Críticos</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="py-2 px-4 border-b text-left">Problema</th>
                                    <th class="py-2 px-4 border-b text-left">Impacto</th>
                                    <th class="py-2 px-4 border-b text-left">Estado</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="py-2 px-4 border-b">Perforadora inoperativa</td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Alto</span>
                                    </td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Pendiente</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 px-4 border-b">Falta de fulminantes</td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Alto</span>
                                    </td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800">En proceso</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 px-4 border-b">Campamento mal ubicado</td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800">Medio</span>
                                    </td>
                                    <td class="py-2 px-4 border-b">
                                        <span class="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Pendiente</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Plan de Acción -->
            <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h3 class="text-lg font-semibold text-blue-800 mb-2">Plan de Acción Inmediato</h3>
                <ul class="list-disc pl-5 text-blue-700">
                    <li>Implementar dos turnos 24/7 para maximizar producción</li>
                    <li>Reubicar campamento operativo para mejorar condiciones</li>
                    <li>Adquirir perforadora de reemplazo con ahorro de personal</li>
                    <li>Reorganizar flujo de materiales y víveres</li>
                    <li>Implementar pago a perforista por tonelada producida</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-blue-900 to-blue-800 text-white p-6 mt-8">
        <div class="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
            <div class="mb-4 md:mb-0">
                <div class="flex items-center">
                    <span class="text-yellow-400 mr-2">⚡</span>
                    <span class="font-semibold">Mina A Dashboard</span>
                </div>
                <p class="text-blue-200 text-sm">Sistema de análisis y gestión minera</p>
            </div>
            <div class="text-blue-200 text-sm">
                <p>© 2025 - Análisis de Eficiencia Operativa</p>
            </div>
        </div>
    </footer>

    <script>
        // Funcionalidad del indicador de análisis
        let isAnalyzing = false;

        function toggleAnalysis() {
            const indicator = document.getElementById('diamondIndicator');
            const statusText = document.getElementById('statusText');

            if (!isAnalyzing) {
                isAnalyzing = true;
                indicator.innerHTML = '⬥';
                indicator.className = 'diamond-indicator analyzing';
                statusText.textContent = 'Analizando...';
                statusText.className = 'text-xs text-orange-600';

                // Simular análisis por 3 segundos
                setTimeout(() => {
                    isAnalyzing = false;
                    indicator.innerHTML = '⬥';
                    indicator.className = 'diamond-indicator';
                    statusText.textContent = 'En espera';
                    statusText.className = 'text-xs text-green-600';
                }, 3000);
            }
        }

        // Inicializar gráfico de producción
        const ctx = document.getElementById('productionChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Actual', 'Objetivo', 'Ideal'],
                datasets: [{
                    label: 'Toneladas',
                    data: [150, 500, 800],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(16, 185, 129, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + ' TN';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                family: 'Inter',
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            callback: function(value) {
                                return value + ' TN';
                            }
                        }
                    }
                },
                layout: {
                    padding: {
                        top: 20,
                        right: 20,
                        bottom: 20,
                        left: 20
                    }
                }
            }
        });
    </script>
</body>
</html>
